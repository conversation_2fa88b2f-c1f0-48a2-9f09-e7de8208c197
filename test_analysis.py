import pandas as pd
import patent_analysis

def test_with_sample():
    """Test the analysis with a small sample of data."""
    print("Loading sample data...")
    data = pd.read_excel('数据.xlsx', nrows=100)  # Load only 100 rows for testing
    
    # Save sample data to a temporary file
    sample_file = '样本数据.xlsx'
    data.to_excel(sample_file, index=False)
    print(f"Sample data saved to {sample_file}")
    
    # Load stopwords
    stopwords_file = '停用词.txt'
    stopwords = patent_analysis.load_stopwords(stopwords_file)
    
    # Calculate BIDF
    print("Calculating BIDF...")
    bidf = patent_analysis.calculate_bidf(data, '年份', '摘要', stopwords)
    
    # Calculate TF-IDF vectors
    print("Calculating TF-IDF vectors...")
    tf_idf_vectors = patent_analysis.calculate_tf_idf_vectors(data, bidf, '年份', '摘要', stopwords)
    
    # Add stock code to vectors
    for vector in tf_idf_vectors:
        patent_id = vector['id']
        stock_code = data[data['id'] == patent_id]['股票代码'].values[0]
        vector['stock_code'] = stock_code
    
    # Calculate similarities
    print("Calculating similarities...")
    tf_idf_vectors_with_sim = patent_analysis.calculate_forward_backward_similarity(tf_idf_vectors)
    
    # Print sample results
    print("\nSample results:")
    for i, vector in enumerate(tf_idf_vectors_with_sim[:5]):
        print(f"Patent {i+1}:")
        print(f"  ID: {vector['id']}")
        print(f"  Year: {vector['year']}")
        print(f"  Top words: {', '.join(vector['top_words'][:5])}")
        print(f"  Forward similarity: {vector['forward_similarity']}")
        print(f"  Backward similarity: {vector['backward_similarity']}")
        print()

if __name__ == "__main__":
    print("Running test analysis...")
    test_with_sample()
    print("Test complete!")
