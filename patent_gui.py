import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import sys
import time
from datetime import datetime
import pandas as pd

# 导入我们的分析模块
import sample_analysis
import batch_analysis

class PatentAnalysisGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("专利数据分析工具 v1.0")
        self.root.geometry("800x700")
        self.root.resizable(True, True)

        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')

        # 创建主框架
        self.create_widgets()

        # 初始化变量
        self.data_file = ""
        self.template_file = ""
        self.stopwords_file = "停用词.txt"
        self.is_running = False

    def create_widgets(self):
        """创建GUI组件"""
        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 主要分析标签页
        self.main_frame = ttk.Frame(notebook)
        notebook.add(self.main_frame, text="数据分析")

        # 设置标签页
        self.settings_frame = ttk.Frame(notebook)
        notebook.add(self.settings_frame, text="设置")

        # 帮助标签页
        self.help_frame = ttk.Frame(notebook)
        notebook.add(self.help_frame, text="帮助")

        self.create_main_tab()
        self.create_settings_tab()
        self.create_help_tab()

    def create_main_tab(self):
        """创建主要分析标签页"""
        # 文件选择区域
        file_frame = ttk.LabelFrame(self.main_frame, text="文件选择", padding=10)
        file_frame.pack(fill=tk.X, padx=10, pady=5)

        # 数据文件选择
        ttk.Label(file_frame, text="专利数据文件:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.data_file_var = tk.StringVar(value="数据.xlsx")
        ttk.Entry(file_frame, textvariable=self.data_file_var, width=50).grid(row=0, column=1, padx=5, pady=2)
        ttk.Button(file_frame, text="浏览", command=self.browse_data_file).grid(row=0, column=2, padx=5, pady=2)

        # 模板文件选择
        ttk.Label(file_frame, text="结果模板文件:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.template_file_var = tk.StringVar(value="结果文件样式.xlsx")
        ttk.Entry(file_frame, textvariable=self.template_file_var, width=50).grid(row=1, column=1, padx=5, pady=2)
        ttk.Button(file_frame, text="浏览", command=self.browse_template_file).grid(row=1, column=2, padx=5, pady=2)

        # 停用词文件选择
        ttk.Label(file_frame, text="停用词文件:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.stopwords_file_var = tk.StringVar(value="停用词.txt")
        ttk.Entry(file_frame, textvariable=self.stopwords_file_var, width=50).grid(row=2, column=1, padx=5, pady=2)
        ttk.Button(file_frame, text="浏览", command=self.browse_stopwords_file).grid(row=2, column=2, padx=5, pady=2)

        # 分析选项区域
        options_frame = ttk.LabelFrame(self.main_frame, text="分析选项", padding=10)
        options_frame.pack(fill=tk.X, padx=10, pady=5)

        # 分析模式选择
        ttk.Label(options_frame, text="分析模式:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.analysis_mode = tk.StringVar(value="sample")
        ttk.Radiobutton(options_frame, text="样本分析（快速测试）", variable=self.analysis_mode, value="sample").grid(row=0, column=1, sticky=tk.W, padx=5)
        ttk.Radiobutton(options_frame, text="完整分析（全部数据）", variable=self.analysis_mode, value="full").grid(row=0, column=2, sticky=tk.W, padx=5)

        # 样本大小设置
        ttk.Label(options_frame, text="样本大小:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.sample_size_var = tk.StringVar(value="1000")
        sample_size_entry = ttk.Entry(options_frame, textvariable=self.sample_size_var, width=10)
        sample_size_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        ttk.Label(options_frame, text="（仅在样本分析模式下有效）").grid(row=1, column=2, sticky=tk.W, padx=5)

        # 输出文件名设置
        ttk.Label(options_frame, text="输出文件名:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.output_file_var = tk.StringVar(value="计算结果.xlsx")
        ttk.Entry(options_frame, textvariable=self.output_file_var, width=30).grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)

        # 控制按钮区域
        control_frame = ttk.Frame(self.main_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=10)

        self.start_button = ttk.Button(control_frame, text="开始分析", command=self.start_analysis, style="Accent.TButton")
        self.start_button.pack(side=tk.LEFT, padx=5)

        self.stop_button = ttk.Button(control_frame, text="停止分析", command=self.stop_analysis, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="打开结果文件", command=self.open_result_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="清空日志", command=self.clear_log).pack(side=tk.RIGHT, padx=5)

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.main_frame, variable=self.progress_var, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, padx=10, pady=5)

        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(self.main_frame, textvariable=self.status_var)
        status_label.pack(pady=5)

        # 日志显示区域
        log_frame = ttk.LabelFrame(self.main_frame, text="运行日志", padding=5)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True)

    def create_settings_tab(self):
        """创建设置标签页"""
        # 停用词编辑区域
        stopwords_frame = ttk.LabelFrame(self.settings_frame, text="停用词编辑", padding=10)
        stopwords_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 按钮区域
        button_frame = ttk.Frame(stopwords_frame)
        button_frame.pack(fill=tk.X, pady=5)

        ttk.Button(button_frame, text="加载停用词", command=self.load_stopwords).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="保存停用词", command=self.save_stopwords).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="重置为默认", command=self.reset_stopwords).pack(side=tk.LEFT, padx=5)

        # 停用词编辑区域
        self.stopwords_text = scrolledtext.ScrolledText(stopwords_frame, height=20, wrap=tk.WORD)
        self.stopwords_text.pack(fill=tk.BOTH, expand=True, pady=5)

        # 加载默认停用词
        self.load_stopwords()

    def create_help_tab(self):
        """创建帮助标签页"""
        help_text = scrolledtext.ScrolledText(self.help_frame, wrap=tk.WORD, height=25)
        help_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        help_content = """
专利数据分析工具使用说明

1. 文件准备：
   - 专利数据文件：包含专利摘要的Excel文件
   - 结果模板文件：定义输出格式的Excel文件
   - 停用词文件：中文停用词列表（可自定义）

2. 分析模式：
   - 样本分析：处理指定数量的数据，用于快速测试
   - 完整分析：处理全部数据，用于正式分析

3. 算法说明：
   - 使用jieba进行中文分词
   - 计算BIDF（时间逆文档频率）
   - 计算TF-IDF向量
   - 计算前向和后向相似度

4. 输出结果：
   - 前向相似度：与未来专利的相似度总和
   - 后向相似度：与过去专利的相似度总和

5. 性能优化：
   - 按股票代码分组处理，避免全局比较
   - 只保存每个专利的前10个关键词
   - 使用批处理技术处理大规模数据

6. 注意事项：
   - 确保数据文件格式正确
   - 大规模数据分析可能需要较长时间
   - 建议先使用样本分析验证结果

7. 技术支持：
   - 如有问题，请查看运行日志
   - 可以随时停止正在运行的分析
   - 支持自定义停用词列表
        """

        help_text.insert(tk.END, help_content)
        help_text.config(state=tk.DISABLED)

    def browse_data_file(self):
        """浏览数据文件"""
        filename = filedialog.askopenfilename(
            title="选择专利数据文件",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if filename:
            self.data_file_var.set(filename)

    def browse_template_file(self):
        """浏览模板文件"""
        filename = filedialog.askopenfilename(
            title="选择结果模板文件",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if filename:
            self.template_file_var.set(filename)

    def browse_stopwords_file(self):
        """浏览停用词文件"""
        filename = filedialog.askopenfilename(
            title="选择停用词文件",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.stopwords_file_var.set(filename)

    def load_stopwords(self):
        """加载停用词"""
        try:
            stopwords_file = self.stopwords_file_var.get()
            if os.path.exists(stopwords_file):
                with open(stopwords_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                self.stopwords_text.delete(1.0, tk.END)
                self.stopwords_text.insert(1.0, content)
            else:
                self.log_message("停用词文件不存在，将创建默认文件")
                self.reset_stopwords()
        except Exception as e:
            messagebox.showerror("错误", f"加载停用词文件失败：{str(e)}")

    def save_stopwords(self):
        """保存停用词"""
        try:
            content = self.stopwords_text.get(1.0, tk.END)
            stopwords_file = self.stopwords_file_var.get()
            with open(stopwords_file, 'w', encoding='utf-8') as f:
                f.write(content)
            self.log_message(f"停用词已保存到 {stopwords_file}")
            messagebox.showinfo("成功", "停用词保存成功！")
        except Exception as e:
            messagebox.showerror("错误", f"保存停用词文件失败：{str(e)}")

    def reset_stopwords(self):
        """重置为默认停用词"""
        default_stopwords = """的
了
和
与
这
那
是
在
有
个
我
他
她
它
们
你
您
我们
他们
她们
它们
这个
那个
这些
那些
一个
一些
一
二
三
四
五
六
七
八
九
十
等
等等
之
之一
之二
之三
以
以及
及
及其
和
与
跟
同
或
或者
而
而且
但
但是
如
如果
若
若是
为
为了
因
因为
所
所以
故
故而
是
是的
不
不是
没
没有
无
无法
可
可以
能
能够
将
将会
已
已经
曾
曾经
正
正在
通过
根据
按照
依据
由于
因此
所以
然后
接着
随后
最后
首先
其次
再次
此外
另外
同时
同样
类似
例如
比如
就是
即是
也就是
也是
还是
还有
只是
只有
仅仅
仅有
如此
这样
那样
一样
一般
普通
常见
常常
时常
一直
始终
从
从而
对
对于
关于
关系
方面
部分
之中
其中
本
该
此
这里
那里
之处
地方
附近
周围
之上
之下
之内
之外
之间
之前
之后
之左
之右
之旁
之侧
之际
之时
之日
之年
之久
之多
之少
之巨
之微
之高
之低
之大
之小
之深
之浅
之厚
之薄
之宽
之窄
之长
之短
之急
之缓
之快
之慢
之早
之晚
之新
之旧
之好
之坏
之多
之少
之难
之易
之贵
之贱
之美
之丑
之雅
之俗
之贤
之愚
之善
之恶
之真
之假
之是
之非
本发明
本实用新型
一种
其特征在于
所述
包括
具有"""
        self.stopwords_text.delete(1.0, tk.END)
        self.stopwords_text.insert(1.0, default_stopwords)

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def validate_files(self):
        """验证文件是否存在"""
        data_file = self.data_file_var.get()
        template_file = self.template_file_var.get()
        stopwords_file = self.stopwords_file_var.get()

        if not os.path.exists(data_file):
            messagebox.showerror("错误", f"数据文件不存在：{data_file}")
            return False

        if not os.path.exists(template_file):
            messagebox.showerror("错误", f"模板文件不存在：{template_file}")
            return False

        # 如果停用词文件不存在，自动创建
        if not os.path.exists(stopwords_file):
            self.log_message(f"停用词文件不存在，将创建默认文件：{stopwords_file}")
            self.save_stopwords()

        return True

    def start_analysis(self):
        """开始分析"""
        if self.is_running:
            messagebox.showwarning("警告", "分析正在进行中，请等待完成或先停止当前分析")
            return

        # 验证文件
        if not self.validate_files():
            return

        # 验证样本大小
        try:
            sample_size = int(self.sample_size_var.get())
            if sample_size <= 0:
                raise ValueError("样本大小必须大于0")
        except ValueError as e:
            messagebox.showerror("错误", f"样本大小设置错误：{str(e)}")
            return

        # 更新UI状态
        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress_bar.start()
        self.status_var.set("分析中...")

        # 清空之前的日志
        self.clear_log()
        self.log_message("开始专利数据分析...")

        # 在新线程中运行分析
        analysis_thread = threading.Thread(target=self.run_analysis)
        analysis_thread.daemon = True
        analysis_thread.start()

    def stop_analysis(self):
        """停止分析"""
        self.is_running = False
        self.log_message("用户请求停止分析...")
        self.status_var.set("正在停止...")

    def run_analysis(self):
        """运行分析（在后台线程中）"""
        try:
            # 获取参数
            data_file = self.data_file_var.get()
            template_file = self.template_file_var.get()
            stopwords_file = self.stopwords_file_var.get()
            output_file = self.output_file_var.get()
            analysis_mode = self.analysis_mode.get()
            sample_size = int(self.sample_size_var.get())

            start_time = time.time()

            if analysis_mode == "sample":
                self.log_message(f"开始样本分析，样本大小：{sample_size}")
                self.run_sample_analysis(data_file, template_file, stopwords_file, output_file, sample_size)
            else:
                self.log_message("开始完整数据分析...")
                self.run_full_analysis(data_file, template_file, stopwords_file, output_file)

            if self.is_running:  # 检查是否被用户停止
                end_time = time.time()
                elapsed_time = end_time - start_time
                self.log_message(f"分析完成！总耗时：{elapsed_time:.2f}秒 ({elapsed_time/60:.2f}分钟)")
                self.log_message(f"结果已保存到：{output_file}")

                # 显示完成对话框
                self.root.after(0, lambda: messagebox.showinfo("完成", f"分析完成！\n结果已保存到：{output_file}"))

        except Exception as e:
            error_msg = f"分析过程中发生错误：{str(e)}"
            self.log_message(error_msg)
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))

        finally:
            # 恢复UI状态
            self.root.after(0, self.analysis_finished)

    def analysis_finished(self):
        """分析完成后的UI更新"""
        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress_bar.stop()
        self.status_var.set("就绪")

    def run_sample_analysis(self, data_file, template_file, stopwords_file, output_file, sample_size):
        """运行样本分析"""
        # 重定向输出到GUI
        original_stdout = sys.stdout

        class GUILogger:
            def __init__(self, gui):
                self.gui = gui

            def write(self, message):
                if message.strip():
                    self.gui.log_message(message.strip())

            def flush(self):
                pass

        sys.stdout = GUILogger(self)

        try:
            # 加载数据
            self.log_message(f"正在加载数据文件：{data_file}")
            data = pd.read_excel(data_file, nrows=sample_size)
            self.log_message(f"已加载{len(data)}条专利数据")

            if not self.is_running:
                return

            # 加载停用词
            stopwords = sample_analysis.load_stopwords(stopwords_file)
            self.log_message(f"已加载{len(stopwords)}个停用词")

            if not self.is_running:
                return

            # 计算BIDF
            self.log_message("开始计算BIDF...")
            bidf = sample_analysis.calculate_bidf(data, '年份', '摘要', stopwords)

            if not self.is_running:
                return

            # 计算TF-IDF向量
            self.log_message("开始计算TF-IDF向量...")
            tf_idf_vectors = sample_analysis.calculate_tf_idf_vectors(data, bidf, '年份', '摘要', stopwords)
            self.log_message(f"已计算{len(tf_idf_vectors)}个专利的TF-IDF向量")

            if not self.is_running:
                return

            # 计算相似度
            self.log_message("开始计算相似度...")
            final_results = sample_analysis.calculate_forward_backward_similarity(tf_idf_vectors)

            if not self.is_running:
                return

            # 准备输出
            self.log_message("准备输出数据...")
            output_data = []
            for result in final_results:
                output_data.append({
                    '股票代码': result['stock_code'],
                    'id': result['id'],
                    '年份': result['year'],
                    '前向相似度（总FS）': result['forward_similarity'],
                    '后向相似度(总BS)': result['backward_similarity']
                })

            # 保存结果
            output_df = pd.DataFrame(output_data)
            template_df = pd.read_excel(template_file)

            final_df = pd.merge(
                template_df[['股票代码', 'id', '年份']],
                output_df,
                on=['股票代码', 'id', '年份'],
                how='left'
            )

            final_df.to_excel(output_file, index=False)

        finally:
            sys.stdout = original_stdout

    def run_full_analysis(self, data_file, template_file, stopwords_file, output_file):
        """运行完整分析"""
        # 重定向输出到GUI
        original_stdout = sys.stdout

        class GUILogger:
            def __init__(self, gui):
                self.gui = gui

            def write(self, message):
                if message.strip():
                    self.gui.log_message(message.strip())

            def flush(self):
                pass

        sys.stdout = GUILogger(self)

        try:
            # 使用批处理分析
            self.log_message(f"正在加载完整数据文件：{data_file}")
            data = pd.read_excel(data_file)
            self.log_message(f"已加载{len(data)}条专利数据")

            if not self.is_running:
                return

            # 加载停用词
            stopwords = batch_analysis.load_stopwords(stopwords_file)
            self.log_message(f"已加载{len(stopwords)}个停用词")

            if not self.is_running:
                return

            # 计算BIDF
            self.log_message("开始计算BIDF...")
            bidf = batch_analysis.calculate_bidf_batch(data, '年份', '摘要', stopwords, batch_size=1000)

            if not self.is_running:
                return

            # 处理专利数据
            batch_size = 1000
            num_batches = (len(data) + batch_size - 1) // batch_size
            all_results = []

            for i in range(num_batches):
                if not self.is_running:
                    return

                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, len(data))
                batch = data.iloc[start_idx:end_idx]

                self.log_message(f"处理批次 {i+1}/{num_batches}...")

                # 处理批次
                batch_results = batch_analysis.process_batch(batch, stopwords)

                # 计算TF-IDF
                batch_results = batch_analysis.calculate_tf_idf_batch(batch_results, bidf)

                all_results.extend(batch_results)

            if not self.is_running:
                return

            # 按股票代码分组计算相似度
            vectors_by_stock = {}
            for result in all_results:
                stock_code = result['stock_code']
                if stock_code not in vectors_by_stock:
                    vectors_by_stock[stock_code] = []
                vectors_by_stock[stock_code].append(result)

            # 计算相似度
            self.log_message("开始计算相似度...")
            final_results = batch_analysis.calculate_similarities_by_stock(vectors_by_stock)

            if not self.is_running:
                return

            # 准备输出
            self.log_message("准备输出数据...")
            output_data = []
            for result in final_results:
                output_data.append({
                    '股票代码': result['stock_code'],
                    'id': result['id'],
                    '年份': result['year'],
                    '前向相似度（总FS）': result['forward_similarity'],
                    '后向相似度(总BS)': result['backward_similarity']
                })

            # 保存结果
            output_df = pd.DataFrame(output_data)
            template_df = pd.read_excel(template_file)

            final_df = pd.merge(
                template_df[['股票代码', 'id', '年份']],
                output_df,
                on=['股票代码', 'id', '年份'],
                how='left'
            )

            final_df.to_excel(output_file, index=False)

        finally:
            sys.stdout = original_stdout

    def open_result_file(self):
        """打开结果文件"""
        output_file = self.output_file_var.get()
        if os.path.exists(output_file):
            try:
                os.startfile(output_file)  # Windows
            except AttributeError:
                try:
                    os.system(f'open "{output_file}"')  # macOS
                except:
                    os.system(f'xdg-open "{output_file}"')  # Linux
        else:
            messagebox.showwarning("警告", f"结果文件不存在：{output_file}")


def main():
    """主函数"""
    root = tk.Tk()
    app = PatentAnalysisGUI(root)

    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap('icon.ico')
    except:
        pass

    # 设置窗口关闭事件
    def on_closing():
        if app.is_running:
            if messagebox.askokcancel("退出", "分析正在进行中，确定要退出吗？"):
                app.is_running = False
                root.destroy()
        else:
            root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)

    # 启动GUI
    root.mainloop()


if __name__ == "__main__":
    main()
