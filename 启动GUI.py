#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
专利数据分析工具 - GUI启动脚本
"""

import sys
import os

def check_dependencies():
    """检查依赖包是否安装"""
    required_packages = [
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('jieba', 'jieba'),
        ('openpyxl', 'openpyxl'),
        ('tkinter', 'tkinter')
    ]
    
    missing_packages = []
    
    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"✓ {package_name} 已安装")
        except ImportError:
            missing_packages.append(package_name)
            print(f"✗ {package_name} 未安装")
    
    if missing_packages:
        print(f"\n缺少以下依赖包：{', '.join(missing_packages)}")
        print("请运行以下命令安装：")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("专利数据分析工具 v1.0")
    print("=" * 50)
    
    # 检查依赖
    print("检查依赖包...")
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    print("\n所有依赖包已安装，启动GUI界面...")
    
    try:
        # 导入并启动GUI
        import patent_gui
        patent_gui.main()
    except Exception as e:
        print(f"启动GUI时发生错误：{e}")
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()
