import pandas as pd
import numpy as np
import jieba
import re
import math
from collections import Counter
import os

def load_stopwords(file_path):
    """Load stopwords from a file."""
    if not os.path.exists(file_path):
        print(f"Warning: Stopwords file {file_path} not found. Using empty stopwords list.")
        return set()

    with open(file_path, 'r', encoding='utf-8') as f:
        stopwords = {line.strip() for line in f if line.strip()}
    return stopwords

def preprocess_text(text, stopwords):
    """Preprocess text by removing punctuation, numbers, and stopwords."""
    if not isinstance(text, str):
        return []

    # Remove punctuation and numbers
    text = re.sub(r'[^\u4e00-\u9fa5]', ' ', text)

    # Segment words using jieba
    words = jieba.lcut(text)

    # Remove stopwords and single characters
    words = [word for word in words if word not in stopwords and len(word) > 1]

    return words

def calculate_word_frequencies(words):
    """Calculate word frequencies."""
    if not words:
        return {}, 0

    word_counts = Counter(words)
    total_words = sum(word_counts.values())

    # Calculate frequency ratios
    word_freq_ratios = {word: count / total_words for word, count in word_counts.items()}

    return word_freq_ratios, total_words

def get_top_n_words(word_freq_ratios, n=10):
    """Get the top N words by frequency."""
    return dict(sorted(word_freq_ratios.items(), key=lambda x: x[1], reverse=True)[:n])

def calculate_bidf(data, year_column, abstract_column, stopwords):
    """Calculate BIDF (Backward Inverse Document Frequency) for each term and year."""
    # Sort data by year
    data = data.sort_values(by=year_column)

    # Initialize counters
    term_doc_counts = {}  # {term: {year: count}}
    total_docs_by_year = {}  # {year: count}

    # Process each document
    for _, row in data.iterrows():
        year = row[year_column]
        abstract = row[abstract_column]

        # Update total docs count for this year
        if year not in total_docs_by_year:
            total_docs_by_year[year] = 0
        total_docs_by_year[year] += 1

        # Process text
        words = preprocess_text(abstract, stopwords)
        unique_words = set(words)

        # Update term document counts
        for word in unique_words:
            if word not in term_doc_counts:
                term_doc_counts[word] = {}

            if year not in term_doc_counts[word]:
                term_doc_counts[word][year] = 0

            term_doc_counts[word][year] += 1

    # Calculate cumulative counts for each year
    years = sorted(total_docs_by_year.keys())
    cumulative_total_docs = {}

    for year in years:
        if year == years[0]:
            cumulative_total_docs[year] = total_docs_by_year[year]
        else:
            cumulative_total_docs[year] = cumulative_total_docs[years[years.index(year) - 1]] + total_docs_by_year[year]

    # Calculate cumulative term doc counts
    cumulative_term_docs = {}

    for term in term_doc_counts:
        cumulative_term_docs[term] = {}

        for year in years:
            if year == years[0]:
                if year in term_doc_counts[term]:
                    cumulative_term_docs[term][year] = term_doc_counts[term][year]
                else:
                    cumulative_term_docs[term][year] = 0
            else:
                prev_year = years[years.index(year) - 1]
                if year in term_doc_counts[term]:
                    cumulative_term_docs[term][year] = cumulative_term_docs[term][prev_year] + term_doc_counts[term][year]
                else:
                    cumulative_term_docs[term][year] = cumulative_term_docs[term][prev_year]

    # Calculate BIDF
    bidf = {}

    for term in cumulative_term_docs:
        bidf[term] = {}
        for year in years:
            if cumulative_term_docs[term][year] > 0:
                bidf[term][year] = math.log(cumulative_total_docs[year] / cumulative_term_docs[term][year])
            else:
                bidf[term][year] = 0

    return bidf

def calculate_tf_idf_vectors(data, bidf, year_column, abstract_column, stopwords, n=10):
    """Calculate TF-IDF vectors for each patent."""
    tf_idf_vectors = []

    for _, row in data.iterrows():
        patent_id = row['id']
        year = row[year_column]
        abstract = row[abstract_column]

        # Process text
        words = preprocess_text(abstract, stopwords)

        # Calculate word frequencies
        word_freq_ratios, _ = calculate_word_frequencies(words)

        # Get top N words
        top_words = get_top_n_words(word_freq_ratios, n)

        # Calculate TF-IDF for each top word
        tf_idf_vector = {}
        for word, tf in top_words.items():
            if word in bidf and year in bidf[word]:
                tf_idf_vector[word] = tf * bidf[word][year]
            else:
                tf_idf_vector[word] = 0

        tf_idf_vectors.append({
            'id': patent_id,
            'year': year,
            'top_words': list(top_words.keys()),
            'tf_values': list(top_words.values()),
            'tf_idf_vector': tf_idf_vector
        })

    return tf_idf_vectors

def calculate_similarity(vector1, vector2):
    """Calculate cosine similarity between two TF-IDF vectors."""
    # Get all unique words from both vectors
    all_words = set(vector1.keys()) | set(vector2.keys())

    # Skip if no words in common
    if not all_words:
        return 0

    # Create numeric vectors
    v1 = np.array([vector1.get(word, 0) for word in all_words])
    v2 = np.array([vector2.get(word, 0) for word in all_words])

    # Calculate cosine similarity
    dot_product = np.dot(v1, v2)
    norm1 = np.linalg.norm(v1)
    norm2 = np.linalg.norm(v2)

    if norm1 == 0 or norm2 == 0:
        return 0

    return dot_product / (norm1 * norm2)

def calculate_forward_backward_similarity(tf_idf_vectors):
    """Calculate forward and backward similarity for each patent."""
    # Group vectors by stock code and sort by year
    vectors_by_stock = {}

    for vector in tf_idf_vectors:
        stock_code = vector['stock_code']
        if stock_code not in vectors_by_stock:
            vectors_by_stock[stock_code] = []
        vectors_by_stock[stock_code].append(vector)

    for stock_code in vectors_by_stock:
        vectors_by_stock[stock_code].sort(key=lambda x: x['year'])

    # Calculate similarities
    for stock_code, vectors in vectors_by_stock.items():
        for i, vector in enumerate(vectors):
            # Forward similarity (with future patents)
            forward_similarities = []
            for j in range(i + 1, len(vectors)):
                sim = calculate_similarity(vector['tf_idf_vector'], vectors[j]['tf_idf_vector'])
                forward_similarities.append(sim)

            # Backward similarity (with past patents)
            backward_similarities = []
            for j in range(0, i):
                sim = calculate_similarity(vector['tf_idf_vector'], vectors[j]['tf_idf_vector'])
                backward_similarities.append(sim)

            # Add to vector
            vector['forward_similarity'] = sum(forward_similarities) if forward_similarities else None
            vector['backward_similarity'] = sum(backward_similarities) if backward_similarities else None

    # Flatten the list
    result = []
    for vectors in vectors_by_stock.values():
        result.extend(vectors)

    return result

def main():
    # Load data
    print("Loading data...")
    data_file = '数据.xlsx'
    data = pd.read_excel(data_file)

    # Load stopwords
    stopwords_file = '停用词.txt'
    stopwords = load_stopwords(stopwords_file)

    # Calculate BIDF
    print("Calculating BIDF...")
    bidf = calculate_bidf(data, '年份', '摘要', stopwords)

    # Calculate TF-IDF vectors
    print("Calculating TF-IDF vectors...")
    tf_idf_vectors = calculate_tf_idf_vectors(data, bidf, '年份', '摘要', stopwords)

    # Add stock code to vectors
    for vector in tf_idf_vectors:
        patent_id = vector['id']
        stock_code = data[data['id'] == patent_id]['股票代码'].values[0]
        vector['stock_code'] = stock_code

    # Calculate similarities
    print("Calculating similarities...")
    tf_idf_vectors_with_sim = calculate_forward_backward_similarity(tf_idf_vectors)

    # Prepare results
    print("Preparing results...")
    results = []

    for vector in tf_idf_vectors_with_sim:
        patent_id = vector['id']
        year = vector['year']
        stock_code = vector['stock_code']

        # Add to results
        results.append({
            '股票代码': stock_code,
            'id': patent_id,
            '年份': year,
            '前向相似度（总FS）': vector['forward_similarity'],
            '后向相似度(总BS)': vector['backward_similarity']
        })

    # Convert to DataFrame
    results_df = pd.DataFrame(results)

    # Save results
    print("Saving results...")
    template_file = '结果文件样式.xlsx'
    template_df = pd.read_excel(template_file)

    # Merge with template structure
    output_df = pd.merge(
        template_df[['股票代码', 'id', '年份']],
        results_df,
        on=['股票代码', 'id', '年份'],
        how='left'
    )

    # Save to Excel
    output_file = '计算结果.xlsx'
    output_df.to_excel(output_file, index=False)

    print(f"Results saved to {output_file}")

if __name__ == "__main__":
    main()
