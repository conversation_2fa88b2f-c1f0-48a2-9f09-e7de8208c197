# 大规模专利数据处理详细说明

## 您提出的核心问题及解决方案

### 1. 数据规模问题：如何处理100万×100万的矩阵？

**问题描述：**
- 10万多条专利数据
- 每个专利提取前10个关键词 = 100万个词汇
- 如果建立全局词汇矩阵 = 100万×100万 = 内存爆炸

**我的解决方案：**
```python
# ❌ 错误做法：建立全局词汇矩阵
# global_matrix = np.zeros((1000000, 1000000))  # 这会导致内存爆炸

# ✅ 正确做法：只存储每个专利的前10个词
tf_idf_vector = {}  # 每个专利只存储10个词的TF-IDF值
for word, tf in top_words.items():  # top_words只有10个
    tf_idf_vector[word] = tf * bidf[word][year]
```

**关键优化：**
1. **维度控制**：每个专利只保留前10个关键词
2. **稀疏存储**：使用字典而不是矩阵存储TF-IDF值
3. **分组处理**：按股票代码分组，避免全局比较

### 2. 分段处理的数据完整性问题

**您的担心完全正确！**

**❌ 错误的分段方式：**
```python
# 这样做是错误的，会导致BIDF计算不准确
batch1 = data[:50000]  # 前5万条
batch2 = data[50000:]  # 后5万条
bidf1 = calculate_bidf(batch1)  # Dt只基于5万条数据 ❌
bidf2 = calculate_bidf(batch2)  # Dt只基于另外5万条数据 ❌
```

**✅ 正确的处理方式：**
```python
# BIDF计算必须使用完整数据集
def calculate_bidf(data, year_column, abstract_column, stopwords):
    # 这里的data必须是完整的数据集
    # 因为Dt（总专利数）和Dw,t（包含词w的专利数）必须基于全部数据
    
    # 可以分批处理文本，但统计信息是全局的
    for _, row in data.iterrows():  # 遍历全部数据
        # 统计每年的专利总数（全局统计）
        total_docs_by_year[year] += 1
        # 统计每个词的出现次数（全局统计）
        term_doc_counts[word][year] += 1
```

**我的批处理策略：**
- **文本处理分批**：分批读取和处理文本，节省内存
- **统计信息全局**：BIDF计算使用完整数据集的统计信息
- **相似度分组**：按股票代码分组计算，避免全局比较

### 3. 时间窗口处理：不是固定的前5年后5年

**您的理解很准确！我的实现是：**

```python
# 不是固定的前5年后5年，而是：
for i, vector in enumerate(vectors):  # 当前专利
    # 前向相似度：与该公司未来所有专利比较
    for j in range(i + 1, len(vectors)):  # 所有未来专利
        future_patent = vectors[j]
        sim = calculate_similarity(current, future_patent)
    
    # 后向相似度：与该公司过去所有专利比较  
    for j in range(0, i):  # 所有过去专利
        past_patent = vectors[j]
        sim = calculate_similarity(current, past_patent)
```

**举例说明：**
- 公司A有专利：2010年、2015年、2018年、2020年、2023年
- 对于2018年的专利：
  - 后向相似度 = 与2010年、2015年专利的相似度总和
  - 前向相似度 = 与2020年、2023年专利的相似度总和

### 4. 停用词的灵活配置

**完全可以随时修改！**

```python
# 停用词文件：停用词.txt（UTF-8编码）
# 每行一个词，可以随时添加或删除
本发明
本实用新型
一种
其特征在于
所述
包括
具有
# ... 更多停用词

# 代码中自动加载
stopwords = load_stopwords('停用词.txt')
```

**修改停用词的方法：**
1. 直接编辑`停用词.txt`文件
2. 每行一个停用词
3. 保存为UTF-8编码
4. 重新运行程序即可

## 大规模数据处理的完整策略

### 内存优化策略

1. **数据分批加载**
```python
# 不是一次性加载全部数据到内存
chunk_size = 1000
for chunk in pd.read_excel('数据.xlsx', chunksize=chunk_size):
    process_chunk(chunk)
```

2. **只保存必要信息**
```python
# 每个专利只保存10个关键词的TF-IDF值
# 而不是保存所有词汇的完整向量
tf_idf_vector = {word: value for word, value in top_10_words.items()}
```

3. **分组处理相似度**
```python
# 按股票代码分组，避免全局比较
# 假设平均每个公司有50个专利
# 复杂度从 O(n²) 降低到 O(k × m²)，其中k是公司数，m是每公司专利数
```

### 计算复杂度分析

**全局比较（错误方式）：**
- 时间复杂度：O(n²) = O(100,000²) = 100亿次比较
- 空间复杂度：O(n × d) = O(100,000 × 1,000,000) = 内存爆炸

**分组比较（我的方式）：**
- 时间复杂度：O(∑(mi²)) ≈ O(k × 50²) = 约250万次比较
- 空间复杂度：O(n × 10) = O(100,000 × 10) = 可控

### 测试和调试策略

1. **样本测试**
```python
sample_size = 1000  # 先用1000条数据测试
# 确认算法正确后再处理全部数据
```

2. **分阶段验证**
```python
# 第一阶段：验证文本处理
# 第二阶段：验证BIDF计算
# 第三阶段：验证TF-IDF计算
# 第四阶段：验证相似度计算
```

3. **性能监控**
```python
# 记录每个阶段的处理时间
# 估算完整数据的处理时间
```

## 使用建议

1. **开始时使用样本分析**：
   ```bash
   python sample_analysis.py  # 处理1000条数据，快速验证
   ```

2. **确认结果正确后使用批处理**：
   ```bash
   python batch_analysis.py  # 处理全部数据
   ```

3. **根据需要调整参数**：
   - 修改`sample_size`调整样本大小
   - 修改`停用词.txt`调整停用词
   - 修改`batch_size`调整批处理大小

4. **监控内存使用**：
   - 如果内存不足，减小batch_size
   - 如果处理太慢，增加batch_size

这样的设计既保证了算法的正确性，又解决了大规模数据的处理问题。
