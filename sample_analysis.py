import pandas as pd
import numpy as np
import jieba
import re
import math
from collections import Counter
import os
import time

def load_stopwords(file_path):
    """
    加载停用词文件

    参数:
        file_path: 停用词文件路径（如 '停用词.txt'）

    返回:
        set: 停用词集合

    注意:
        - 停用词文件可以随时修改，每行一个词
        - 如果文件不存在，会返回空集合并给出警告
        - 支持UTF-8编码的中文停用词
    """
    if not os.path.exists(file_path):
        print(f"Warning: Stopwords file {file_path} not found. Using empty stopwords list.")
        return set()

    with open(file_path, 'r', encoding='utf-8') as f:
        stopwords = {line.strip() for line in f if line.strip()}
    return stopwords

def preprocess_text(text, stopwords):
    """
    文本预处理：分词、去除标点符号、数字和停用词

    参数:
        text: 原始文本（专利摘要）
        stopwords: 停用词集合

    返回:
        list: 处理后的词汇列表

    处理步骤:
        1. 去除所有非中文字符（标点、数字、英文等）
        2. 使用jieba进行中文分词
        3. 去除停用词和单字符词汇
    """
    if not isinstance(text, str):
        return []

    # 去除标点符号和数字，只保留中文字符
    text = re.sub(r'[^\u4e00-\u9fa5]', ' ', text)

    # 使用jieba进行中文分词
    words = jieba.lcut(text)

    # 去除停用词和单字符词汇（单字符通常意义不大）
    words = [word for word in words if word not in stopwords and len(word) > 1]

    return words

def calculate_word_frequencies(words):
    """
    计算词频和词频占比

    参数:
        words: 词汇列表

    返回:
        tuple: (词频占比字典, 总词数)

    说明:
        - 词频占比 = 某词出现次数 / 总词数
        - 这个比例就是TF-IDF公式中的"词频占比"部分
    """
    if not words:
        return {}, 0

    word_counts = Counter(words)
    total_words = sum(word_counts.values())

    # 计算词频占比（TF部分）
    word_freq_ratios = {word: count / total_words for word, count in word_counts.items()}

    return word_freq_ratios, total_words

def get_top_n_words(word_freq_ratios, n=10):
    """
    获取词频最高的前N个词

    参数:
        word_freq_ratios: 词频占比字典
        n: 取前N个词（默认10个）

    返回:
        dict: 前N个词及其词频占比

    说明:
        - 按词频占比降序排列
        - 每个专利只保留前10个关键词，避免维度爆炸
    """
    return dict(sorted(word_freq_ratios.items(), key=lambda x: x[1], reverse=True)[:n])

def calculate_bidf(data, year_column, abstract_column, stopwords):
    """
    计算BIDF（时间逆文档频率）

    参数:
        data: 完整的专利数据集（注意：必须是完整数据，不能分段！）
        year_column: 年份列名
        abstract_column: 摘要列名
        stopwords: 停用词集合

    返回:
        dict: {词汇: {年份: BIDF值}}

    核心算法:
        BIDF(w,t) = log(Dt / Dw,t)
        其中：
        - Dt: 截止到时间t之前的专利总数
        - Dw,t: 截止到时间t之前包含术语w的专利数

    重要说明:
        这里必须使用完整数据集！如果分段处理会导致Dt和Dw,t计算错误
        因为每个专利的BIDF值依赖于历史上所有专利的统计信息
    """
    print("开始计算BIDF，使用完整数据集确保统计准确性...")

    # 按年份排序，确保时间顺序正确
    data = data.sort_values(by=year_column)

    # 初始化计数器
    term_doc_counts = {}  # {词汇: {年份: 包含该词的文档数}}
    total_docs_by_year = {}  # {年份: 该年份的文档总数}

    # 遍历每个专利文档，统计词汇出现情况
    for _, row in data.iterrows():
        year = row[year_column]
        abstract = row[abstract_column]

        # 统计每年的专利总数
        if year not in total_docs_by_year:
            total_docs_by_year[year] = 0
        total_docs_by_year[year] += 1

        # 处理文本，提取词汇
        words = preprocess_text(abstract, stopwords)
        unique_words = set(words)  # 去重，每个专利中同一个词只计算一次

        # 统计每个词在每年出现的文档数
        for word in unique_words:
            if word not in term_doc_counts:
                term_doc_counts[word] = {}

            if year not in term_doc_counts[word]:
                term_doc_counts[word][year] = 0

            term_doc_counts[word][year] += 1

    # 计算累积统计数据（这是BIDF计算的核心）
    years = sorted(total_docs_by_year.keys())
    cumulative_total_docs = {}

    # 计算截止到每年之前（不包括当年）的专利总数 Dt
    print("计算历史专利总数...")
    for i, year in enumerate(years):
        if i == 0:
            # 第一年之前没有专利，Dt = 0
            cumulative_total_docs[year] = 0
        else:
            # 当年之前的所有专利数量
            prev_years = years[:i]
            cumulative_total_docs[year] = sum(total_docs_by_year[y] for y in prev_years)

    # 计算累积词汇文档统计
    cumulative_term_docs = {}

    print("计算每个词汇的历史出现次数...")
    for term in term_doc_counts:
        cumulative_term_docs[term] = {}

        # 计算截止到每年之前（不包括当年）包含术语term的专利数 Dw,t
        for i, year in enumerate(years):
            if i == 0:
                # 第一年之前没有专利，Dw,t = 0
                cumulative_term_docs[term][year] = 0
            else:
                # 当年之前包含术语term的专利数
                prev_years = years[:i]
                cumulative_term_docs[term][year] = sum(term_doc_counts[term].get(y, 0) for y in prev_years)

    # 计算BIDF值
    bidf = {}

    print("计算BIDF值...")
    for term in cumulative_term_docs:
        bidf[term] = {}
        for year in years:
            Dt = cumulative_total_docs[year]  # 截止到年份t之前的专利总数
            Dwt = cumulative_term_docs[term][year]  # 截止到年份t之前包含词w的专利数

            if Dt > 0 and Dwt > 0:
                # BIDF(w,t) = log(Dt / Dw,t)
                bidf[term][year] = math.log(Dt / Dwt)
            else:
                # 如果分母为0或者没有历史数据，BIDF设为0
                bidf[term][year] = 0

    print(f"BIDF计算完成，共处理{len(bidf)}个不同词汇")
    return bidf

def calculate_tf_idf_vectors(data, bidf, year_column, abstract_column, stopwords, n=10):
    """Calculate TF-IDF vectors for each patent."""
    tf_idf_vectors = []

    for _, row in data.iterrows():
        patent_id = row['id']
        year = row[year_column]
        abstract = row[abstract_column]
        stock_code = row['股票代码']

        # Process text
        words = preprocess_text(abstract, stopwords)

        # Calculate word frequencies
        word_freq_ratios, _ = calculate_word_frequencies(words)

        # Get top N words
        top_words = get_top_n_words(word_freq_ratios, n)

        # Calculate TF-IDF for each top word
        tf_idf_vector = {}
        for word, tf in top_words.items():
            if word in bidf and year in bidf[word]:
                tf_idf_vector[word] = tf * bidf[word][year]
            else:
                tf_idf_vector[word] = 0

        tf_idf_vectors.append({
            'id': patent_id,
            'year': year,
            'stock_code': stock_code,
            'top_words': list(top_words.keys()),
            'tf_values': list(top_words.values()),
            'tf_idf_vector': tf_idf_vector
        })

    return tf_idf_vectors

def calculate_similarity(vector1, vector2):
    """Calculate cosine similarity between two TF-IDF vectors."""
    # Get all unique words from both vectors
    all_words = set(vector1.keys()) | set(vector2.keys())

    # Skip if no words in common
    if not all_words:
        return 0

    # Create numeric vectors
    v1 = np.array([vector1.get(word, 0) for word in all_words])
    v2 = np.array([vector2.get(word, 0) for word in all_words])

    # Calculate cosine similarity
    dot_product = np.dot(v1, v2)
    norm1 = np.linalg.norm(v1)
    norm2 = np.linalg.norm(v2)

    if norm1 == 0 or norm2 == 0:
        return 0

    return dot_product / (norm1 * norm2)

def calculate_forward_backward_similarity(tf_idf_vectors):
    """
    计算前向和后向相似度

    参数:
        tf_idf_vectors: 包含TF-IDF向量的专利列表

    返回:
        list: 包含相似度信息的专利列表

    核心逻辑:
        1. 按股票代码分组（同一公司的专利才比较）
        2. 在每个公司内部按年份排序
        3. 对于每个专利：
           - 前向相似度：与该公司未来所有专利的相似度总和
           - 后向相似度：与该公司过去所有专利的相似度总和

    重要说明:
        - 不是固定的前5年后5年，而是与该公司所有历史/未来专利比较
        - 这样避免了100万×100万的全局比较，大大降低了计算复杂度
        - 每个公司内部的专利数量通常不会太大，所以计算是可行的
    """
    print("开始计算相似度，按股票代码分组处理...")

    # 按股票代码分组，避免全局比较
    vectors_by_stock = {}

    for vector in tf_idf_vectors:
        stock_code = vector['stock_code']
        if stock_code not in vectors_by_stock:
            vectors_by_stock[stock_code] = []
        vectors_by_stock[stock_code].append(vector)

    # 在每个股票代码内部按年份排序
    for stock_code in vectors_by_stock:
        vectors_by_stock[stock_code].sort(key=lambda x: x['year'])

    print(f"共有{len(vectors_by_stock)}个不同的股票代码需要处理")

    # 计算相似度
    results = []

    for stock_code, vectors in vectors_by_stock.items():
        print(f"正在处理股票代码 {stock_code}，共有{len(vectors)}个专利...")

        for i, vector in enumerate(vectors):
            # 前向相似度：与未来专利的相似度总和
            forward_similarities = []
            for j in range(i + 1, len(vectors)):
                future_patent = vectors[j]
                sim = calculate_similarity(vector['tf_idf_vector'], future_patent['tf_idf_vector'])
                forward_similarities.append(sim)

            # 后向相似度：与过去专利的相似度总和
            backward_similarities = []
            for j in range(0, i):
                past_patent = vectors[j]
                sim = calculate_similarity(vector['tf_idf_vector'], past_patent['tf_idf_vector'])
                backward_similarities.append(sim)

            # 计算总相似度
            vector['forward_similarity'] = sum(forward_similarities) if forward_similarities else None
            vector['backward_similarity'] = sum(backward_similarities) if backward_similarities else None

            results.append(vector)

    print("相似度计算完成")
    return results

def main():
    """
    主函数：执行完整的专利分析流程

    处理流程:
        1. 加载样本数据（可调整样本大小）
        2. 加载停用词
        3. 计算BIDF（使用完整数据集）
        4. 计算TF-IDF向量
        5. 计算相似度
        6. 输出结果

    大规模数据处理策略:
        - 样本分析：处理前N条数据，快速验证算法
        - 内存优化：只保存必要的TF-IDF向量（每专利10个词）
        - 分组计算：按股票代码分组，避免全局比较
    """
    start_time = time.time()

    # 加载样本数据进行测试
    print("正在加载样本数据...")
    data_file = '数据.xlsx'
    sample_size = 1000  # 可以调整这个数字：1000=快速测试，10000=中等规模，全部=完整分析
    data = pd.read_excel(data_file, nrows=sample_size)
    print(f"已加载{len(data)}条专利数据进行分析")

    # 加载停用词（可以随时修改停用词.txt文件）
    stopwords_file = '停用词.txt'
    stopwords = load_stopwords(stopwords_file)
    print(f"已加载{len(stopwords)}个停用词")

    # 计算BIDF（这里使用样本数据，实际应用中应使用完整数据）
    print("开始计算BIDF...")
    bidf = calculate_bidf(data, '年份', '摘要', stopwords)

    # 计算每个专利的TF-IDF向量
    print("开始计算TF-IDF向量...")
    tf_idf_vectors = calculate_tf_idf_vectors(data, bidf, '年份', '摘要', stopwords)
    print(f"已计算{len(tf_idf_vectors)}个专利的TF-IDF向量")

    # 计算相似度（按股票代码分组，避免全局比较）
    print("开始计算相似度...")
    final_results = calculate_forward_backward_similarity(tf_idf_vectors)

    # 准备输出数据
    print("准备输出数据...")
    output_data = []

    for result in final_results:
        output_data.append({
            '股票代码': result['stock_code'],
            'id': result['id'],
            '年份': result['year'],
            '前向相似度（总FS）': result['forward_similarity'],
            '后向相似度(总BS)': result['backward_similarity']
        })

    # 转换为DataFrame
    output_df = pd.DataFrame(output_data)

    # 加载结果模板
    template_file = '结果文件样式.xlsx'
    template_df = pd.read_excel(template_file)

    # 与模板结构合并
    final_df = pd.merge(
        template_df[['股票代码', 'id', '年份']],
        output_df,
        on=['股票代码', 'id', '年份'],
        how='left'
    )

    # 保存结果
    output_file = '样本计算结果.xlsx'
    final_df.to_excel(output_file, index=False)

    # 计算处理时间
    end_time = time.time()
    elapsed_time = end_time - start_time

    print(f"结果已保存到 {output_file}")
    print(f"总处理时间: {elapsed_time:.2f} 秒 ({elapsed_time/60:.2f} 分钟)")
    print(f"平均每个专利处理时间: {elapsed_time/len(data):.3f} 秒")

if __name__ == "__main__":
    main()
