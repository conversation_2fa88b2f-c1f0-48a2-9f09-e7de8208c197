# 专利数据分析计算工具

这个项目实现了专利数据的分析和计算，包括关键词提取、TF-IDF向量计算以及专利之间的相似度计算。

## 功能说明

1. **关键词提取**：
   - 从专利摘要中提取关键词
   - 计算每个专利中词频最高的前10个词
   - 计算每个词在专利中的词频占比

2. **TF-IDF向量计算**：
   - 计算BIDF（时间逆文档频率）
   - 计算每个专利的TF-IDF向量

3. **相似度计算**：
   - 计算前向相似度（与未来专利的相似度）
   - 计算后向相似度（与过去专利的相似度）

## 文件说明

- `patent_analysis.py`: 主要分析代码
- `batch_analysis.py`: 批处理版本，适用于大数据集
- `run_analysis.py`: 运行分析的简单脚本
- `test_analysis.py`: 测试脚本，使用小样本数据测试功能
- `停用词.txt`: 中文停用词列表

## 使用方法

1. 确保数据文件 `数据.xlsx` 和结果模板 `结果文件样式.xlsx` 在当前目录下
2. 运行分析脚本：

```bash
python batch_analysis.py
```

3. 分析完成后，结果将保存在 `计算结果.xlsx` 文件中

## 计算公式

1. **BIDF计算公式**：
   - BIDF(w,t) = log(Dt / Dw,t)
   - 其中，Dt: 截止到时间t前的专利总数
   - Dw,t: 截止到时间t前包含术语w的专利数

2. **TF-IDF计算公式**：
   - TF-IDF(w,i) = 词频占比 × BIDF(w,t)
   - 每个专利的TF-IDF向量为1*10的矩阵（对应10个关键词）

3. **相似度计算**：
   - 使用余弦相似度计算专利之间的相似性
   - 前向相似度：与未来专利的相似度总和
   - 后向相似度：与过去专利的相似度总和

## 性能优化

为了处理大量数据，代码实现了以下优化：

1. 批处理：将数据分批处理，减少内存占用
2. 高效的文本处理：使用jieba分词和正则表达式优化文本处理
3. 向量化计算：使用NumPy进行向量计算，提高计算效率
