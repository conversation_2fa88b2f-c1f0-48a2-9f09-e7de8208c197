# 专利数据分析计算工具 v1.0

这个项目实现了专利数据的分析和计算，包括关键词提取、TF-IDF向量计算以及专利之间的相似度计算。提供了命令行和图形界面两种使用方式。

## ✨ 主要功能

1. **关键词提取**：
   - 从专利摘要中提取关键词
   - 计算每个专利中词频最高的前10个词
   - 计算每个词在专利中的词频占比

2. **TF-IDF向量计算**：
   - 计算BIDF（时间逆文档频率）
   - 计算每个专利的TF-IDF向量

3. **相似度计算**：
   - 计算前向相似度（与未来专利的相似度）
   - 计算后向相似度（与过去专利的相似度）

4. **图形界面**：
   - 用户友好的GUI界面
   - 实时日志显示
   - 可自定义停用词
   - 支持样本分析和完整分析

## 📁 文件说明

### 核心分析模块
- `sample_analysis.py`: 样本分析代码（带详细注释）
- `batch_analysis.py`: 批处理版本，适用于大数据集
- `patent_analysis.py`: 基础分析代码

### 用户界面
- `patent_gui.py`: 图形用户界面（推荐使用）
- `run_analysis.py`: 命令行运行脚本
- `test_analysis.py`: 测试脚本

### 配置文件
- `停用词.txt`: 中文停用词列表（可自定义）
- `requirements.txt`: Python依赖包列表

### 文档
- `README.md`: 使用说明（本文件）
- `大规模数据处理说明.md`: 详细技术说明

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装Python依赖
pip install -r requirements.txt
```

### 2. 准备数据文件

确保以下文件在工作目录中：
- `数据.xlsx`: 包含专利数据的Excel文件
- `结果文件样式.xlsx`: 结果模板文件

### 3. 运行方式

#### 方式一：图形界面（推荐）

```bash
python patent_gui.py
```

**GUI界面功能：**
- 📁 文件选择：选择数据文件、模板文件、停用词文件
- ⚙️ 分析选项：样本分析或完整分析
- 📊 实时进度：进度条和详细日志
- ✏️ 停用词编辑：可视化编辑停用词
- 📖 帮助文档：内置使用说明

#### 方式二：命令行

```bash
# 样本分析（快速测试）
python sample_analysis.py

# 完整分析（全部数据）
python batch_analysis.py
```

## 📊 计算公式

### 1. BIDF计算公式
```
BIDF(w,t) = log(Dt / Dw,t)
```
其中：
- `Dt`: 截止到时间t之前的专利总数
- `Dw,t`: 截止到时间t之前包含术语w的专利数

### 2. TF-IDF计算公式
```
TF-IDF(w,i) = 词频占比 × BIDF(w,t)
```
每个专利的TF-IDF向量为1×10的矩阵（对应10个关键词）

### 3. 相似度计算
- 使用余弦相似度计算专利之间的相似性
- **前向相似度**：与该公司未来所有专利的相似度总和
- **后向相似度**：与该公司过去所有专利的相似度总和

## ⚡ 性能优化

为了处理大规模数据（10万+专利），实现了以下优化：

### 1. 内存优化
- **维度控制**：每个专利只保留前10个关键词
- **稀疏存储**：使用字典而非矩阵存储TF-IDF值
- **批处理**：分批处理数据，避免内存溢出

### 2. 计算优化
- **分组处理**：按股票代码分组，避免全局比较
- **时间复杂度**：从O(n²)降低到O(k×m²)，其中k是公司数，m是每公司专利数
- **并行处理**：支持多线程处理

### 3. 数据完整性
- **BIDF计算**：使用完整数据集确保统计准确性
- **分段策略**：只在文本处理时分批，统计信息保持全局

## 🛠️ 自定义配置

### 停用词自定义
1. 编辑`停用词.txt`文件
2. 每行一个停用词
3. 保存为UTF-8编码
4. 重新运行程序即可

### 参数调整
- **样本大小**：在GUI中调整或修改代码中的`sample_size`
- **批处理大小**：修改`batch_size`参数
- **关键词数量**：修改`n=10`参数

## 📈 使用建议

1. **首次使用**：
   - 先运行样本分析验证结果
   - 检查停用词是否合适
   - 确认输出格式正确

2. **大规模数据**：
   - 使用批处理模式
   - 监控内存使用情况
   - 预留足够的处理时间

3. **结果验证**：
   - 检查前向/后向相似度的合理性
   - 验证关键词提取质量
   - 对比不同参数设置的结果

## 🔧 故障排除

### 常见问题

1. **内存不足**：
   - 减小批处理大小
   - 使用样本分析模式
   - 关闭其他程序释放内存

2. **处理速度慢**：
   - 检查数据文件大小
   - 使用SSD硬盘
   - 增加批处理大小

3. **结果异常**：
   - 检查数据文件格式
   - 验证停用词设置
   - 查看详细日志信息

### 技术支持

如遇到问题，请：
1. 查看GUI界面的运行日志
2. 检查`大规模数据处理说明.md`文档
3. 验证输入文件格式和内容

## 📄 许可证

本项目仅供学习和研究使用。
