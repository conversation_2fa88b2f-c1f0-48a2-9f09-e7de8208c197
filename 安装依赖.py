#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
专利数据分析工具 - 依赖安装脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ {package} 安装失败")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("专利数据分析工具 - 依赖安装")
    print("=" * 50)
    
    # 检查pip是否可用
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "--version"])
    except subprocess.CalledProcessError:
        print("错误：pip不可用，请先安装pip")
        input("按回车键退出...")
        return
    
    # 读取requirements.txt
    if os.path.exists("requirements.txt"):
        print("从requirements.txt安装依赖包...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print("✓ 所有依赖包安装完成")
        except subprocess.CalledProcessError:
            print("✗ 安装过程中出现错误")
    else:
        # 手动安装核心包
        packages = [
            "pandas>=1.3.0",
            "numpy>=1.21.0", 
            "openpyxl>=3.0.7",
            "jieba>=0.42.1",
            "python-docx>=0.8.11"
        ]
        
        print("安装核心依赖包...")
        success_count = 0
        
        for package in packages:
            if install_package(package):
                success_count += 1
        
        print(f"\n安装完成：{success_count}/{len(packages)} 个包安装成功")
    
    print("\n验证安装...")
    
    # 验证安装
    test_imports = [
        ("pandas", "数据处理"),
        ("numpy", "数值计算"),
        ("jieba", "中文分词"),
        ("openpyxl", "Excel文件处理"),
        ("tkinter", "图形界面")
    ]
    
    all_ok = True
    for module, desc in test_imports:
        try:
            __import__(module)
            print(f"✓ {desc} ({module}) 可用")
        except ImportError:
            print(f"✗ {desc} ({module}) 不可用")
            all_ok = False
    
    if all_ok:
        print("\n🎉 所有依赖包安装成功！可以运行专利分析工具了。")
        print("请运行：python 启动GUI.py 或双击 启动专利分析工具.bat")
    else:
        print("\n⚠️ 部分依赖包安装失败，请手动安装缺失的包。")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
